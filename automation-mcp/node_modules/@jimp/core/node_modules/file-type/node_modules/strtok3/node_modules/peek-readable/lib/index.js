"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.StreamReader = exports.EndOfStreamError = void 0;
var EndOfFileStream_1 = require("./EndOfFileStream");
Object.defineProperty(exports, "EndOfStreamError", { enumerable: true, get: function () { return EndOfFileStream_1.EndOfStreamError; } });
var StreamReader_1 = require("./StreamReader");
Object.defineProperty(exports, "StreamReader", { enumerable: true, get: function () { return StreamReader_1.StreamReader; } });
