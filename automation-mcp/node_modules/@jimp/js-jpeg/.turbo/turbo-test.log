

> @jimp/js-jpeg@1.1.2 test /Users/<USER>/Documents/jimp/plugins/js-jpeg
> vitest "--watch=false"


[7m[1m[36m RUN [39m[22m[27m [36mv2.0.5[39m [90m/Users/<USER>/Documents/jimp/plugins/js-jpeg[39m

[?25l [90m·[39m [2msrc/[22mindex[2m.test.ts[22m[2m (3)[22m
   [90m·[39m JPEG[2m (3)[22m
     [90m·[39m load JPG
     [90m·[39m load JPG with fill bytes
     [90m·[39m export JPG
[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (3)[22m[33m 340[2mms[22m[39m
   [32m✓[39m JPEG[2m (3)[22m[33m 339[2mms[22m[39m
     [32m✓[39m load JPG
     [32m✓[39m load JPG with fill bytes
     [32m✓[39m export JPG
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (3)[22m[33m 340[2mms[22m[39m
   [32m✓[39m JPEG[2m (3)[22m[33m 339[2mms[22m[39m
     [32m✓[39m load JPG
     [32m✓[39m load JPG with fill bytes
     [32m✓[39m export JPG

[2m Test Files [22m [1m[32m1 passed[39m[22m[90m (1)[39m
[2m      Tests [22m [1m[32m3 passed[39m[22m[90m (3)[39m
[2m   Start at [22m 01:33:45
[2m   Duration [22m 2.48s[2m (transform 377ms, setup 0ms, collect 729ms, tests 340ms, environment 0ms, prepare 540ms)[22m

[?25h[?25h
