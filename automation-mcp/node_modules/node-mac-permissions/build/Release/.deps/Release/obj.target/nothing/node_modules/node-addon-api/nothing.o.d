cmd_Release/obj.target/nothing/node_modules/node-addon-api/nothing.o := cc -o Release/obj.target/nothing/node_modules/node-addon-api/nothing.o ../node_modules/node-addon-api/nothing.c '-DNODE_GYP_MODULE_NAME=nothing' '-DUSING_UV_SHARED=1' '-DUSING_V8_SHARED=1' '-DV8_DEPRECATION_WARNINGS=1' '-D_GLIBCXX_USE_CXX11_ABI=1' '-D_FILE_OFFSET_BITS=64' '-D_DARWIN_USE_64_BIT_INODE=1' '-D_LARGEFILE_SOURCE' -I/Users/<USER>/Library/Caches/node-gyp/24.3.0/include/node -I/Users/<USER>/Library/Caches/node-gyp/24.3.0/src -I/Users/<USER>/Library/Caches/node-gyp/24.3.0/deps/openssl/config -I/Users/<USER>/Library/Caches/node-gyp/24.3.0/deps/openssl/openssl/include -I/Users/<USER>/Library/Caches/node-gyp/24.3.0/deps/uv/include -I/Users/<USER>/Library/Caches/node-gyp/24.3.0/deps/zlib -I/Users/<USER>/Library/Caches/node-gyp/24.3.0/deps/v8/include  -O3 -gdwarf-2 -fno-strict-aliasing -mmacosx-version-min=13.5 -arch arm64 -Wall -Wendif-labels -W -Wno-unused-parameter  -MMD -MF ./Release/.deps/Release/obj.target/nothing/node_modules/node-addon-api/nothing.o.d.raw   -c
Release/obj.target/nothing/node_modules/node-addon-api/nothing.o: \
  ../node_modules/node-addon-api/nothing.c
../node_modules/node-addon-api/nothing.c:
