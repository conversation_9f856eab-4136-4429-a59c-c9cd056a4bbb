cmd_Release/obj.target/permissions/permissions.o := c++ '-DNODE_GYP_MODULE_NAME=permissions' '-DUSING_UV_SHARED=1' '-DUSING_V8_SHARED=1' '-DV8_DEPRECATION_WARNINGS=1' '-D_GLIBCXX_USE_CXX11_ABI=1' '-D_FILE_OFFSET_BITS=64' '-D_DARWIN_USE_64_BIT_INODE=1' '-D_LARGEFILE_SOURCE' '-DNAPI_DISABLE_CPP_EXCEPTIONS' '-DBUILDING_NODE_EXTENSION' -I/Users/<USER>/Library/Caches/node-gyp/24.3.0/include/node -I/Users/<USER>/Library/Caches/node-gyp/24.3.0/src -I/Users/<USER>/Library/Caches/node-gyp/24.3.0/deps/openssl/config -I/Users/<USER>/Library/Caches/node-gyp/24.3.0/deps/openssl/openssl/include -I/Users/<USER>/Library/Caches/node-gyp/24.3.0/deps/uv/include -I/Users/<USER>/Library/Caches/node-gyp/24.3.0/deps/zlib -I/Users/<USER>/Library/Caches/node-gyp/24.3.0/deps/v8/include -I/Users/<USER>/MCP/automation-mcp/node_modules/node-mac-permissions/node_modules/node-addon-api  -O3 -gdwarf-2 -fno-strict-aliasing -mmacosx-version-min=10.13 -arch arm64 -Wall -Wendif-labels -W -Wno-unused-parameter -std=gnu++20 -stdlib=libc++ -fno-rtti -fno-exceptions -std=c++14 -stdlib=libc++  -MMD -MF ./Release/.deps/Release/obj.target/permissions/permissions.o.d.raw -c -o Release/obj.target/permissions/permissions.o ../permissions.mm
Release/obj.target/permissions/permissions.o: ../permissions.mm \
  /Users/<USER>/MCP/automation-mcp/node_modules/node-mac-permissions/node_modules/node-addon-api/napi.h \
  /Users/<USER>/Library/Caches/node-gyp/24.3.0/include/node/node_api.h \
  /Users/<USER>/Library/Caches/node-gyp/24.3.0/include/node/js_native_api.h \
  /Users/<USER>/Library/Caches/node-gyp/24.3.0/include/node/js_native_api_types.h \
  /Users/<USER>/Library/Caches/node-gyp/24.3.0/include/node/node_api_types.h \
  /Users/<USER>/MCP/automation-mcp/node_modules/node-mac-permissions/node_modules/node-addon-api/napi-inl.h \
  /Users/<USER>/MCP/automation-mcp/node_modules/node-mac-permissions/node_modules/node-addon-api/napi-inl.deprecated.h
../permissions.mm:
/Users/<USER>/MCP/automation-mcp/node_modules/node-mac-permissions/node_modules/node-addon-api/napi.h:
/Users/<USER>/Library/Caches/node-gyp/24.3.0/include/node/node_api.h:
/Users/<USER>/Library/Caches/node-gyp/24.3.0/include/node/js_native_api.h:
/Users/<USER>/Library/Caches/node-gyp/24.3.0/include/node/js_native_api_types.h:
/Users/<USER>/Library/Caches/node-gyp/24.3.0/include/node/node_api_types.h:
/Users/<USER>/MCP/automation-mcp/node_modules/node-mac-permissions/node_modules/node-addon-api/napi-inl.h:
/Users/<USER>/MCP/automation-mcp/node_modules/node-mac-permissions/node_modules/node-addon-api/napi-inl.deprecated.h:
